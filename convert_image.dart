import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

void main() async {
  // Initialiser Flutter
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Charger l'image AVIF
    final ByteData data = await rootBundle.load('assets/images/ccp-rip-logo.avif');
    final Uint8List bytes = data.buffer.asUint8List();
    
    // Décoder l'image
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;
    
    // Convertir en PNG
    final ByteData? pngData = await image.toByteData(format: ui.ImageByteFormat.png);
    if (pngData != null) {
      final File file = File('assets/images/ccp-rip-logo.png');
      await file.writeAsBytes(pngData.buffer.asUint8List());
      print('Image convertie avec succès en PNG');
    } else {
      print('Échec de la conversion en PNG');
    }
  } catch (e) {
    print('Erreur: $e');
  }
  
  // Quitter l'application
  exit(0);
}
