# 🔐 Configuration Keystore pour CCP RIP App

## 📋 Résumé

Ce document contient toutes les informations nécessaires pour la signature et la publication de l'application CCP RIP sur Google Play Store.

## 🔑 Informations du Keystore

### Fichiers générés :
- **Keystore** : `android/app/ccp-rip-keystore.jks`
- **Certificat PEM** : `upload_certificate.pem`
- **Configuration** : `android/key.properties`

### Détails du certificat :
- **Nom** : ZAITRI Ameur
- **Organisation** : ENAGEO
- **Unité** : DES
- **Ville** : ELIDRISSIA
- **État/Province** : DJELFA
- **Pays** : DZ (Algérie)
- **Alias** : upload
- **Algorithme** : RSA 2048 bits
- **Validité** : 10 000 jours (~27 ans)

### Mots de passe :
- **Store Password** : ccprip2025
- **Key Password** : ccprip2025

## 📤 Upload sur Google Play Store

### 1. Certificat à uploader
Le fichier `upload_certificate.pem` contient le certificat public à uploader sur Google Play Console dans :
- **Play Console** → **Configuration de l'application** → **Signature de l'application** → **Upload du certificat**

### 2. Compilation Release
Pour générer un APK signé pour production :
```bash
flutter build apk --release
```

Le fichier sera généré dans : `build/app/outputs/flutter-apk/app-release.apk`

### 3. Compilation AAB (recommandé pour Play Store)
Pour générer un Android App Bundle :
```bash
flutter build appbundle --release
```

## 🔧 Configuration technique

### android/key.properties
```properties
storePassword=ccprip2025
keyPassword=ccprip2025
keyAlias=upload
storeFile=ccp-rip-keystore.jks
```

### android/app/build.gradle.kts
La configuration de signature a été ajoutée automatiquement avec :
- Import des propriétés du keystore
- Configuration de signature pour les releases
- Utilisation du keystore personnalisé

## ⚠️ Sécurité

### Fichiers à protéger :
- `android/app/ccp-rip-keystore.jks` - **NE JAMAIS PARTAGER**
- `android/key.properties` - **NE JAMAIS COMMITER**
- Mots de passe - **GARDER SECRETS**

### Sauvegarde recommandée :
- Sauvegarder le keystore dans un lieu sûr
- Noter les mots de passe dans un gestionnaire de mots de passe
- Conserver une copie du certificat.pem

## 📱 Application ID
- **Package Name** : `com.my.cleribccp`
- **Version Code** : Géré automatiquement par Flutter
- **Version Name** : Définie dans pubspec.yaml

## ✅ Tests effectués
- ✅ Génération du keystore réussie
- ✅ Export du certificat.pem réussi
- ✅ Configuration build.gradle.kts validée
- ✅ Compilation release APK réussie
- ✅ Signature automatique fonctionnelle

## 🚀 Prochaines étapes

1. **Upload du certificat** sur Google Play Console
2. **Compilation AAB** pour la première publication
3. **Test de l'APK signé** sur un appareil réel
4. **Publication** sur Google Play Store

---

**Date de création** : 2 septembre 2025
**Créé pour** : CCP RIP Algeria App
**Développeur** : ZAITRI Ameur
