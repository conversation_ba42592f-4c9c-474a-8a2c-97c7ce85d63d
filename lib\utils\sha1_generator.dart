import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SHA1Generator extends StatefulWidget {
  const SHA1Generator({super.key});

  @override
  State<SHA1Generator> createState() => _SHA1GeneratorState();
}

class _SHA1GeneratorState extends State<SHA1Generator> {
  String _sha1 = 'Chargement...';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _getSHA1();
  }

  Future<void> _getSHA1() async {
    try {
      const platform = MethodChannel('app.channel.shared.data');
      final String result = await platform.invokeMethod('getSHA1Certificate');
      setState(() {
        _sha1 = result;
        _isLoading = false;
      });
    } on PlatformException catch (e) {
      setState(() {
        _sha1 = "Erreur: ${e.message}";
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _sha1 = "Erreur: $e";
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Générateur SHA-1'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Column(
                  children: [
                    const Text(
                      'Empreinte SHA-1:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    SelectableText(
                      _sha1,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: _sha1));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Empreinte SHA-1 copiée dans le presse-papiers')),
                        );
                      },
                      icon: const Icon(Icons.copy),
                      label: const Text('Copier'),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
