import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../models/ccp_account.dart';

class LocalBackupService {
  // Generate filename with current date in ddmmyyyy format
  static String get _backupFileName {
    final now = DateTime.now();
    final day = now.day.toString().padLeft(2, '0');
    final month = now.month.toString().padLeft(2, '0');
    final year = now.year.toString();
    return 'backup-ccp-rip_$day$month$year.json';
  }
  
  // Singleton pattern
  static final LocalBackupService _instance = LocalBackupService._internal();
  factory LocalBackupService() => _instance;
  LocalBackupService._internal();
  
  // Export accounts to a JSON file and share it
  Future<bool> exportAccounts(List<CCPAccount> accounts, BuildContext context) async {
    try {
      // Create a temporary file with the accounts data
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$_backupFileName');
      
      // Convert accounts to JSON
      final accountsJson = accounts.map((account) => account.toJson()).toList();
      final jsonString = jsonEncode(accountsJson);
      
      // Write to file
      await file.writeAsString(jsonString);
      
      // Share the file
      final result = await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'CCP RIP Accounts Backup',
        text: 'This is a backup of your CCP RIP accounts. Keep this file safe to restore your accounts later.',
      );
      
      return result.status == ShareResultStatus.success;
    } catch (error) {
      debugPrint('Error exporting accounts: $error');
      return false;
    }
  }
  
  // Import accounts from a JSON file
  Future<List<CCPAccount>?> importAccountsFromJson(String jsonString) async {
    try {
      // Parse JSON
      final List<dynamic> jsonData = jsonDecode(jsonString);
      final List<CCPAccount> accounts = [];
      
      // Convert JSON to CCPAccount objects
      for (var json in jsonData) {
        try {
          accounts.add(CCPAccount.fromJson(json));
        } catch (e) {
          debugPrint('Error parsing account: $e');
        }
      }
      
      return accounts;
    } catch (error) {
      debugPrint('Error importing accounts: $error');
      return null;
    }
  }
}
