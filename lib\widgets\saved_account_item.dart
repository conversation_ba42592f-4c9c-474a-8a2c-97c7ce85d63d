import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../models/ccp_account.dart';
import '../providers/expansion_provider.dart';
import '../utils/text_direction_helper.dart';

/// Widget pour afficher un compte CCP enregistré
class SavedAccountItem extends StatefulWidget {
  /// Le compte CCP à afficher
  final CCPAccount account;

  /// Fonction de rappel lorsque le compte est supprimé
  final Function(CCPAccount) onDelete;

  /// Fonction de rappel lorsque le compte est modifié
  final Function(CCPAccount) onEdit;

  const SavedAccountItem({
    super.key,
    required this.account,
    required this.onDelete,
    required this.onEdit,
  });

  @override
  State<SavedAccountItem> createState() => _SavedAccountItemState();
}

class _SavedAccountItemState extends State<SavedAccountItem> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  // Identifiant unique pour ce compte
  String get _accountId => widget.account.id.toString();

  // Vérifie si ce compte est actuellement développé
  bool get _isExpanded => Provider.of<ExpansionProvider>(context, listen: true).isExpanded(_accountId);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0, end: 0.5).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Mettre à jour l'état de l'animation en fonction de l'état d'expansion
    if (_isExpanded) {
      _animationController.value = 1.0;
    } else {
      _animationController.value = 0.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Supprime les zéros à gauche du numéro CCP
  String _formatCcpNumber(String ccpNumber) {
    // Supprimer les zéros à gauche mais garder au moins un chiffre
    return ccpNumber.replaceFirst(RegExp(r'^0+'), '') == '' ? '0' : ccpNumber.replaceFirst(RegExp(r'^0+'), '');
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias, // Pour s'assurer que rien ne dépasse des bords arrondis
      child: InkWell(
        onTap: () {
          final expansionProvider = Provider.of<ExpansionProvider>(context, listen: false);
          final isCurrentlyExpanded = expansionProvider.isExpanded(_accountId);

          // Si ce compte est déjà développé, le fermer
          // Sinon, fermer les autres et développer celui-ci
          if (isCurrentlyExpanded) {
            expansionProvider.setExpandedAccount(null);
          } else {
            expansionProvider.setExpandedAccount(_accountId);
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec nom du propriétaire et date (toujours visible)
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFFFEC33), // Jaune RGB 255, 236, 51
                boxShadow: _isExpanded
                  ? [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(180),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.account_circle_outlined,
                            size: 16,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            widget.account.ownerName,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      if (!_isExpanded)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(180),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: Colors.black12,
                              width: 0.5,
                            ),
                          ),
                          child: TextDirectionHelper.forceLeftToRight(
                            Text(
                              _formatCcpNumber(widget.account.ccpNumber),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                                fontFamily: 'monospace', // Utiliser une police monospace pour les chiffres
                              ),
                            ),
                          ),
                        ),
                      const SizedBox(width: 6),
                      RotationTransition(
                        turns: _rotationAnimation,
                        child: const Icon(
                          Icons.keyboard_arrow_down,
                          size: 20,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Contenu détaillé (visible uniquement si développé)
            AnimatedCrossFade(
              firstChild: const SizedBox(height: 0),
              secondChild: Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date de modification
                    Row(
                      children: [
                        const Icon(
                          Icons.update_outlined,
                          size: 12,
                          color: Colors.black45,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${AppLocalizations.of(context).savedAccountLastModified} ${dateFormat.format(widget.account.dateModified)}',
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.black45,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 14),

                    // Numéro CCP et clé
                    _buildInfoRow(
                      context,
                      icon: Icons.credit_card_outlined,
                      label: AppLocalizations.of(context).savedAccountCcpLabel,
                      value: '${_formatCcpNumber(widget.account.ccpNumber)} -- ${widget.account.ccpKey.toString().padLeft(2, '0')}',
                    ),
                    const SizedBox(height: 14),

                    // Code RIP
                    _buildInfoRow(
                      context,
                      icon: Icons.account_balance_wallet_outlined,
                      label: AppLocalizations.of(context).savedAccountRipLabel,
                      value: widget.account.ripCode,
                      isRip: true,
                    ),

                    const SizedBox(height: 16),

                    // Boutons d'action
                    Container(
                      width: double.infinity, // Prend toute la largeur disponible
                      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey.withAlpha(15),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Colors.grey.withAlpha(30),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildActionButton(
                            context,
                            icon: Icons.content_copy_outlined,
                            label: AppLocalizations.of(context).savedAccountCopyRip,
                            onPressed: () => _copyRIP(context),
                          ),
                          _buildActionButton(
                            context,
                            icon: Icons.share_outlined,
                            label: AppLocalizations.of(context).savedAccountShare,
                            onPressed: () => _shareRIP(context),
                          ),
                          _buildActionButton(
                            context,
                            icon: Icons.edit_outlined,
                            label: AppLocalizations.of(context).savedAccountEdit,
                            onPressed: () => widget.onEdit(widget.account),
                          ),
                          _buildActionButton(
                            context,
                            icon: Icons.delete_outline,
                            label: AppLocalizations.of(context).savedAccountDelete,
                            onPressed: () => _confirmDelete(context),
                            color: Colors.red,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              crossFadeState: _isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 300),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit une ligne d'information
  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    bool isRip = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label avec icône
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: const Color(0xFFFFEC33),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 12,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),

        // Valeur (différente pour RIP et CCP)
        if (!isRip)
          // Valeur normale pour CCP
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(20),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.grey.withAlpha(40),
                width: 0.5,
              ),
            ),
            child: TextDirectionHelper.forceLeftToRight(
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold, // Texte en gras
                  fontFamily: 'monospace', // Utiliser une police monospace pour les chiffres
                ),
              ),
            ),
          )
        else
          // Valeur spéciale pour RIP
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFFFFEC33).withAlpha(50),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: const Color(0xFFFFEC33).withAlpha(150),
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.centerLeft,
                    // Utiliser TextDirectionHelper pour forcer l'affichage de gauche à droite
                    child: TextDirectionHelper.forceLeftToRight(
                      Text(
                        _formatRipCode(value),
                        style: const TextStyle(
                          fontSize: 12, // Taille réduite
                          fontFamily: 'monospace',
                          letterSpacing: 0.0, // Espacement minimal
                          fontWeight: FontWeight.bold, // Texte en gras
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                ),
                // Espacement adaptatif selon la langue
                SizedBox(
                  width: Localizations.localeOf(context).languageCode == 'ar' ? 12 : 8,
                ),

                // Icône de code QR
                GestureDetector(
                  onTap: () => _showQRCode(context),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: const Color(0xBBFFFFFF), // Blanc avec 73% d'opacité
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: const Color(0x33000000), // Noir avec 20% d'opacité
                        width: 0.5,
                      ),
                    ),
                    child: const Icon(
                      Icons.qr_code,
                      size: 16,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// Construit un bouton d'action
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    final buttonColor = color ?? Theme.of(context).primaryColor;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: buttonColor,
                size: 18,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.w500,
                  color: buttonColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Copier le code RIP dans le presse-papiers (sans espaces)
  void _copyRIP(BuildContext context) {
    final loc = AppLocalizations.of(context);

    // Supprimer les espaces pour le format de copie
    final textToCopy = widget.account.ripCode.replaceAll(' ', '');
    Clipboard.setData(ClipboardData(text: textToCopy));

    // Afficher un SnackBar avec animation (en vert comme dans la page de vérification)
    final successColor = const Color(0xFF4CAF50); // Vert pour les résultats corrects
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(loc.savedAccountRipCopied),
          ],
        ),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        backgroundColor: successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Partager le code RIP
  void _shareRIP(BuildContext context) {
    final loc = AppLocalizations.of(context);
    Share.share(
      '${loc.savedAccountRipLabel} ${widget.account.ownerName}: ${widget.account.ripCode}',
      subject: loc.savedAccountRipLabel,
    );
  }

  /// Afficher le code QR du RIP
  void _showQRCode(BuildContext context) {
    final ripCode = widget.account.ripCode.replaceAll(' ', '');
    final primaryColor = Theme.of(context).primaryColor;
    final accentColor = const Color(0xFFFFEC33); // Jaune CCP

    // Désactiver le clavier avant d'afficher le dialog
    FocusScope.of(context).unfocus();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // En-tête avec logo et titre
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.qr_code_scanner,
                        color: primaryColor,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Code RIP QR',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: () {
                        // Désactiver le clavier avant de fermer
                        FocusScope.of(context).unfocus();
                        Navigator.of(context).pop();
                      },
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Code QR avec bordure jaune
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: accentColor,
                      width: 3,
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x1A000000), // Noir avec 10% d'opacité
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: QrImageView(
                    data: ripCode,
                    version: QrVersions.auto,
                    size: 200.0,
                    backgroundColor: Colors.white,
                    eyeStyle: QrEyeStyle(
                      eyeShape: QrEyeShape.square,
                      color: primaryColor,
                    ),
                    dataModuleStyle: QrDataModuleStyle(
                      dataModuleShape: QrDataModuleShape.square,
                      color: primaryColor,
                    ),
                    padding: const EdgeInsets.all(8),
                    embeddedImage: const AssetImage('assets/images/ccp-rip-logo.avif'),
                    embeddedImageStyle: QrEmbeddedImageStyle(
                      size: const Size(40, 40),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Nom du propriétaire
                Text(
                  widget.account.ownerName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 8),

                // Code RIP avec formatage amélioré
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0x33FFEC33), // Jaune avec 20% d'opacité
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: accentColor,
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: TextDirectionHelper.forceLeftToRight(
                      Text(
                        _formatRipCode(ripCode),
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12, // Taille encore plus réduite
                          color: primaryColor,
                          letterSpacing: 0.0, // Espacement minimal
                          fontWeight: FontWeight.bold, // Texte en gras
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Formater le code RIP pour une meilleure lisibilité
  String _formatRipCode(String code) {
    if (code.length != 20) return code;
    // Utiliser un espace très fin entre les parties
    const thinSpace = '\u2006'; // Espace fin
    return '${code.substring(0, 3)}$thinSpace${code.substring(3, 8)}$thinSpace${code.substring(8, 18)}$thinSpace${code.substring(18, 20)}';
  }



  /// Afficher une boîte de dialogue de confirmation avant de supprimer le compte
  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          // Supprimer complètement le bouton de licences
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          dialogTheme: const DialogThemeData(
            // Styles pour le dialogue
            titleTextStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            contentTextStyle: TextStyle(fontSize: 14),
          ),
        ),
        child: AlertDialog(
          title: Text(
            AppLocalizations.of(context).savedAccountDeleteTitle,
            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          content: Text(
            AppLocalizations.of(context).savedAccountDeleteConfirm(widget.account.ownerName),
            style: const TextStyle(color: Colors.black),
          ),
          contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
          actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          // Ajouter deux boutons : Annuler et Supprimer
          actions: [
            // Bouton Annuler
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[700],
                padding: const EdgeInsets.symmetric(horizontal: 16),
                textStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              child: Text(AppLocalizations.of(context).savedAccountsDialogCancel),
            ),
            // Bouton Supprimer
            TextButton(
              onPressed: () {
                // Fermer la boîte de dialogue
                Navigator.of(context).pop();
                // Appeler la fonction onDelete pour supprimer le compte
                widget.onDelete(widget.account);
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                textStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              child: Text(AppLocalizations.of(context).savedAccountDelete),
            ),
          ],
        ),
      ),
    );
  }


}
