/// Classe utilitaire pour calculer les clés CCP et RIP pour les comptes postaux algériens
class CCPCalculator {
  /// Calcule la clé CCP pour un numéro CCP donné
  ///
  /// La clé CCP est calculée en utilisant l'algorithme spécifique pour les comptes postaux algériens
  /// Retourne un nombre à deux chiffres (00-99)
  static int calculateCCPKey(String ccpNumber) {
    // Supprimer les espaces ou caractères non numériques
    final cleanCcpNumber = ccpNumber.replaceAll(RegExp(r'[^0-9]'), '');

    if (cleanCcpNumber.isEmpty) {
      throw ArgumentError('Le numéro CCP ne peut pas être vide');
    }

    // Algorithme pour le calcul de la clé CCP selon le script fourni:
    // Implémentation de la fonction CCP(x) du script VBA

    List<int> TCCP1 = [];
    for (int i = 0; i < cleanCcpNumber.length; i++) {
      TCCP1.add(int.parse(cleanCcpNumber[i]));
    }

    int ac = 0;
    int j = 0;

    for (int ind = 4; ind < 4 + cleanCcpNumber.length; ind++) {
      ac += (TCCP1[cleanCcpNumber.length - 1 - j] * ind);
      j++;
    }

    // Prendre les deux derniers chiffres
    ac = ac % 100;

    return ac;
  }

  /// Calcule la clé RIP pour un numéro CCP donné
  ///
  /// La clé RIP est calculée en utilisant l'algorithme spécifique pour les comptes postaux algériens
  /// Retourne un nombre à deux chiffres (00-99)
  static int calculateRIPKey(String ccpNumber) {
    // Supprimer les espaces ou caractères non numériques
    final cleanCcpNumber = ccpNumber.replaceAll(RegExp(r'[^0-9]'), '');

    if (cleanCcpNumber.isEmpty) {
      throw ArgumentError('Le numéro CCP ne peut pas être vide');
    }

    // Algorithme pour le calcul de la clé RIP selon le script fourni:
    // nResulta = (97 - ((((CLng(Compte_CCP) Mod 97) * (3 Mod 97)) + 85) Mod 97))

    // Convertir en nombre (peut être trop grand pour int, donc on utilise une approche modulaire)
    int ccpMod97 = 0;
    for (int i = 0; i < cleanCcpNumber.length; i++) {
      ccpMod97 = (ccpMod97 * 10 + int.parse(cleanCcpNumber[i])) % 97;
    }

    // Calculer selon la formule
    int result = (97 - ((((ccpMod97) * (3 % 97)) + 85) % 97));

    // Assurer que le résultat est entre 0 et 99
    result = result % 100;

    return result;
  }

  /// Formate un numéro CCP avec sa clé
  ///
  /// Retourne le numéro CCP formaté avec sa clé (ex: "12345 67")
  static String formatCCPWithKey(String ccpNumber) {
    final cleanCcpNumber = ccpNumber.replaceAll(RegExp(r'[^0-9]'), '');
    final ccpKey = calculateCCPKey(cleanCcpNumber);

    // Format: Numéro CCP + espace + clé CCP (formatée sur 2 chiffres)
    final paddedCcpKey = ccpKey.toString().padLeft(2, '0');
    return "$cleanCcpNumber $paddedCcpKey";
  }

  /// Génère le code RIP complet à partir d'un numéro CCP
  ///
  /// Retourne le code RIP complet au format "007 99999 XXXXXXXXXX YY"
  /// où XXXXXXXXXX est le numéro CCP (formaté sur 10 chiffres) et YY est la clé RIP
  static String generateRIPCode(String ccpNumber) {
    final cleanCcpNumber = ccpNumber.replaceAll(RegExp(r'[^0-9]'), '');
    final ripKey = calculateRIPKey(cleanCcpNumber);

    // Formater le numéro CCP sur 10 chiffres avec des zéros devant
    final paddedCcpNumber = cleanCcpNumber.padLeft(10, '0');

    // Format: "007 99999 " + numéro CCP formaté + " " + clé RIP (formatée sur 2 chiffres)
    final paddedRipKey = ripKey.toString().padLeft(2, '0');
    return "007 99999 $paddedCcpNumber $paddedRipKey";
  }

  /// Vérifie si un numéro CCP est valide
  ///
  /// Retourne true si le numéro CCP est valide, false sinon
  static bool isValidCCPNumber(String ccpNumber) {
    final cleanCcpNumber = ccpNumber.replaceAll(RegExp(r'[^0-9]'), '');
    return cleanCcpNumber.isNotEmpty && cleanCcpNumber.length <= 10;
  }
}
