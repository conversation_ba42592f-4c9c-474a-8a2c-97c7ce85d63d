#!/usr/bin/env python3
"""
Serveur web simple pour servir l'application Flutter web
"""
import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Configuration
PORT = 8080
WEB_DIR = "build/web"

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=WEB_DIR, **kwargs)
    
    def end_headers(self):
        # Ajouter les en-têtes CORS pour éviter les problèmes de sécurité
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()

def main():
    # Vérifier que le dossier web existe
    if not os.path.exists(WEB_DIR):
        print(f"❌ Le dossier {WEB_DIR} n'existe pas.")
        print("Exécutez d'abord: flutter build web")
        return
    
    # Démarrer le serveur
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 Serveur démarré sur http://localhost:{PORT}")
        print(f"📁 Servant le contenu de: {os.path.abspath(WEB_DIR)}")
        print("🌐 Ouverture du navigateur...")
        
        # Ouvrir automatiquement le navigateur
        webbrowser.open(f"http://localhost:{PORT}")
        
        print("⏹️  Appuyez sur Ctrl+C pour arrêter le serveur")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Serveur arrêté")

if __name__ == "__main__":
    main()
