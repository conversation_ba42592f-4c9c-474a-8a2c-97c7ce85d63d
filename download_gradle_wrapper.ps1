# Script pour télécharger le wrapper Gradle
$wrapperUrl = "https://services.gradle.org/distributions/gradle-8.10.2-wrapper.jar"
$wrapperPath = "android\gradle\wrapper\gradle-wrapper.jar"

Write-Host "Téléchargement du wrapper Gradle..."
try {
    Invoke-WebRequest -Uri $wrapperUrl -OutFile $wrapperPath
    Write-Host "Wrapper Gradle téléchargé avec succès !" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors du téléchargement : $($_.Exception.Message)" -ForegroundColor Red
    
    # Alternative : télécharger depuis GitHub
    Write-Host "Tentative avec une source alternative..."
    $altUrl = "https://github.com/gradle/gradle/raw/v8.10.2/gradle/wrapper/gradle-wrapper.jar"
    try {
        Invoke-WebRequest -Uri $altUrl -OutFile $wrapperPath
        Write-Host "Wrapper Gradle téléchargé depuis la source alternative !" -ForegroundColor Green
    } catch {
        Write-Host "Échec du téléchargement depuis toutes les sources." -ForegroundColor Red
    }
}
