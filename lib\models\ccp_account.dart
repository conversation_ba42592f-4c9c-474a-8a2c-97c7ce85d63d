/// Model class representing a CCP account with its associated information
class CCPAccount {
  /// Unique identifier for the account in the database
  final int? id;

  /// The CCP number without spaces or special characters
  final String ccpNumber;

  /// The name of the account owner
  final String ownerName;

  /// The calculated CCP key
  final int ccpKey;

  /// The calculated RIP key
  final int ripKey;

  /// The complete RIP code in the format "007 99999 XXXXXXXX YY"
  final String ripCode;

  /// The date when the account was created or last modified
  final DateTime dateModified;

  CCPAccount({
    this.id,
    required this.ccpNumber,
    required this.ownerName,
    required this.ccpKey,
    required this.ripKey,
    required this.ripCode,
    DateTime? dateModified,
  }) : dateModified = dateModified ?? DateTime.now();

  /// Creates a copy of this CCPAccount with the given fields replaced with the new values
  CCPAccount copyWith({
    int? id,
    String? ccpNumber,
    String? ownerName,
    int? ccpKey,
    int? ripKey,
    String? ripCode,
    DateTime? dateModified,
  }) {
    return CCPAccount(
      id: id ?? this.id,
      ccpNumber: ccpNumber ?? this.ccpNumber,
      ownerName: ownerName ?? this.ownerName,
      ccpKey: ccpKey ?? this.ccpKey,
      ripKey: ripKey ?? this.ripKey,
      ripCode: ripCode ?? this.ripCode,
      dateModified: dateModified ?? this.dateModified,
    );
  }

  /// Converts this CCPAccount to a Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'ccpNumber': ccpNumber,
      'ownerName': ownerName,
      'ccpKey': ccpKey,
      'ripKey': ripKey,
      'ripCode': ripCode,
      'dateModified': dateModified.toIso8601String(),
    };
  }

  /// Creates a CCPAccount from a Map (used for database operations)
  factory CCPAccount.fromMap(Map<String, dynamic> map) {
    return CCPAccount(
      id: map['id'] as int?,
      ccpNumber: map['ccpNumber'] as String,
      ownerName: map['ownerName'] as String,
      ccpKey: map['ccpKey'] as int,
      ripKey: map['ripKey'] as int,
      ripCode: map['ripCode'] as String,
      dateModified: DateTime.parse(map['dateModified'] as String),
    );
  }

  /// Converts this CCPAccount to a Map for JSON serialization
  Map<String, dynamic> toJson() => toMap();

  /// Creates a CCPAccount from a JSON Map
  factory CCPAccount.fromJson(Map<String, dynamic> json) {
    return CCPAccount.fromMap(json);
  }

  @override
  String toString() {
    return 'CCPAccount(id: $id, ccpNumber: $ccpNumber, ownerName: $ownerName, '
        'ccpKey: $ccpKey, ripKey: $ripKey, ripCode: $ripCode, '
        'dateModified: $dateModified)';
  }
}
