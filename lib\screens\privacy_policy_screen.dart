import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../l10n/app_localizations.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  String _appVersion = '2.2.0';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      // Garder la version par défaut si erreur
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);
    final languageCode = Localizations.localeOf(context).languageCode;
    final textDirection =
        languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr;

    return Scaffold(
      appBar: AppBar(
        title: Text(loc.menuPrivacyPolicy),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec version auto-détectée
            _buildHeader(languageCode),

            const SizedBox(height: 24),

            // Sommaire
            _buildSummary(languageCode),

            const SizedBox(height: 24),

            // Introduction
            _buildSection(
              _getIntroductionTitle(languageCode),
              _getIntroductionContent(languageCode),
              Icons.info_outline,
              Colors.blue,
              textDirection,
            ),

            // Objectif de l'application
            _buildSection(
              _getObjectiveTitle(languageCode),
              _getObjectiveContent(languageCode),
              Icons.flag_outlined,
              Colors.green,
              textDirection,
            ),

            // Collecte et utilisation des données
            _buildSection(
              _getDataCollectionTitle(languageCode),
              _getDataCollectionContent(languageCode),
              Icons.storage_outlined,
              Colors.purple,
              textDirection,
            ),

            // Publicités Google AdMob
            _buildSection(
              _getAdsTitle(languageCode),
              _getAdsContent(languageCode),
              Icons.ads_click_outlined,
              Colors.orange,
              textDirection,
            ),

            // Services tiers utilisés
            _buildSection(
              _getThirdPartyTitle(languageCode),
              _getThirdPartyContent(languageCode),
              Icons.cloud_outlined,
              Colors.indigo,
              textDirection,
            ),

            // Permissions nécessaires
            _buildSection(
              _getPermissionsTitle(languageCode),
              _getPermissionsContent(languageCode),
              Icons.security_outlined,
              Colors.red,
              textDirection,
            ),

            // Sécurité des données
            _buildSection(
              _getSecurityTitle(languageCode),
              _getSecurityContent(languageCode),
              Icons.shield_outlined,
              Colors.teal,
              textDirection,
            ),

            // Conservation des données
            _buildSection(
              _getRetentionTitle(languageCode),
              _getRetentionContent(languageCode),
              Icons.schedule_outlined,
              Colors.brown,
              textDirection,
            ),

            // Droits des utilisateurs
            _buildSection(
              _getUserRightsTitle(languageCode),
              _getUserRightsContent(languageCode),
              Icons.person_outline,
              Colors.cyan,
              textDirection,
            ),

            // Cookies
            _buildSection(
              _getCookiesTitle(languageCode),
              _getCookiesContent(languageCode),
              Icons.cookie_outlined,
              Colors.amber,
              textDirection,
            ),

            // Liens vers d'autres sites
            _buildSection(
              _getLinksTitle(languageCode),
              _getLinksContent(languageCode),
              Icons.link_outlined,
              Colors.deepPurple,
              textDirection,
            ),

            // Confidentialité des enfants
            _buildSection(
              _getChildrenTitle(languageCode),
              _getChildrenContent(languageCode),
              Icons.child_care_outlined,
              Colors.pink,
              textDirection,
            ),

            // Modifications de cette politique
            _buildSection(
              _getChangesTitle(languageCode),
              _getChangesContent(languageCode),
              Icons.update_outlined,
              Colors.lime,
              textDirection,
            ),

            // Contact
            _buildSection(
              _getContactTitle(languageCode),
              _getContactContent(languageCode),
              Icons.contact_mail_outlined,
              Colors.blueGrey,
              textDirection,
              isLast: true,
            ),

            const SizedBox(height: 24),

            // Lien vers la politique complète
            _buildPolicyLink(languageCode),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(String languageCode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(Icons.privacy_tip_outlined, size: 40, color: Colors.white),
          const SizedBox(height: 12),
          Text(
            _getTitle(languageCode),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _getLastUpdated(languageCode),
            style: const TextStyle(fontSize: 12, color: Colors.white70),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSummary(String languageCode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.list_alt,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _getSummaryTitle(languageCode),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ..._getSummaryItems(
            languageCode,
          ).map((item) => _buildSummaryItem(item)),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(title, style: const TextStyle(fontSize: 14)),
    );
  }

  Widget _buildSection(
    String title,
    String content,
    IconData icon,
    Color color,
    TextDirection textDirection, {
    bool isLast = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: const TextStyle(
              fontSize: 13,
              height: 1.5,
              color: Colors.black87,
            ),
            textDirection: textDirection,
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyLink(String languageCode) {
    return GestureDetector(
      onTap: () async {
        const String urlString =
            'https://ccp-rip.blogspot.com/p/privacy-policy.html';
        final Uri url = Uri.parse(urlString);
        try {
          bool launched = await launchUrl(url);
          if (!launched) {
            launched = await launchUrl(
              url,
              mode: LaunchMode.externalApplication,
            );
          }
          if (!launched) {
            launched = await launchUrl(url, mode: LaunchMode.inAppBrowserView);
          }
          if (!launched && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Impossible d\'ouvrir le lien'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erreur: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.open_in_new, color: Colors.white, size: 18),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                _getPolicyLinkText(languageCode),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthodes de traduction
  String _getTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Privacy Policy – CCP RIP';
      case 'ar':
        return 'سياسة الخصوصية – CCP RIP';
      default:
        return 'Politique de Confidentialité – CCP RIP';
    }
  }

  String _getLastUpdated(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Last updated: August 25, 2025 · Version: $_appVersion · Contact: <EMAIL>';
      case 'ar':
        return 'آخر تحديث: 25 أغسطس 2025 · الإصدار: $_appVersion · الاتصال: <EMAIL>';
      default:
        return 'Dernière mise à jour : 25 Aout 2025 · Version : $_appVersion · Contact : <EMAIL>';
    }
  }

  String _getSummaryTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Summary';
      case 'ar':
        return 'الملخص';
      default:
        return 'Sommaire';
    }
  }

  List<String> _getSummaryItems(String languageCode) {
    switch (languageCode) {
      case 'en':
        return [
          '1. Introduction',
          '2. Application purpose',
          '3. Data collection and use',
          '4. Google AdMob advertising',
          '5. Third-party services used',
          '6. Required permissions',
          '7. Data security',
          '8. Data retention',
          '9. User rights',
          '10. Cookies',
          '11. Links to other sites',
          '12. Children\'s privacy',
          '13. Policy changes',
          '14. Contact',
        ];
      case 'ar':
        return [
          '1. المقدمة',
          '2. الغرض من التطبيق',
          '3. جمع البيانات واستخدامها',
          '4. إعلانات Google AdMob',
          '5. الخدمات الخارجية المستخدمة',
          '6. الأذونات المطلوبة',
          '7. أمان البيانات',
          '8. الاحتفاظ بالبيانات',
          '9. حقوق المستخدم',
          '10. ملفات تعريف الارتباط',
          '11. روابط لمواقع أخرى',
          '12. خصوصية الأطفال',
          '13. تغييرات السياسة',
          '14. الاتصال',
        ];
      default:
        return [
          '1. Introduction',
          '2. Objectif de l\'application',
          '3. Collecte et utilisation des données',
          '4. Publicités Google AdMob',
          '5. Services tiers utilisés',
          '6. Permissions nécessaires',
          '7. Sécurité des données',
          '8. Conservation des données',
          '9. Droits des utilisateurs',
          '10. Cookies',
          '11. Liens vers d\'autres sites',
          '12. Confidentialité des enfants',
          '13. Modifications de cette politique',
          '14. Contact',
        ];
    }
  }

  String _getIntroductionTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Introduction';
      case 'ar':
        return 'المقدمة';
      default:
        return 'Introduction';
    }
  }

  String _getIntroductionContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Welcome to the CCP RIP application. This policy explains how we collect, use and protect your information when using our application for calculating RIP for CCP accounts.\n\nWe respect your privacy and are committed to protecting your personal data.\n\nDisclaimer: This application is not affiliated with Algérie Poste or any government organization. It is only a tool intended to assist users.';
      case 'ar':
        return 'مرحبًا بكم في تطبيق CCP RIP. تشرح هذه السياسة كيفية جمع واستخدام وحماية معلوماتكم عند استخدام تطبيقنا لحساب RIP لحسابات CCP.\n\nنحن نحترم خصوصيتكم ونلتزم بحماية بياناتكم الشخصية.\n\nإخلاء المسؤولية: هذا التطبيق غير مرتبط ببريد الجزائر أو أي منظمة حكومية. إنه مجرد أداة مخصصة لمساعدة المستخدمين.';
      default:
        return 'Bienvenue dans l\'application CCP RIP. Cette politique explique comment nous collectons, utilisons et protégeons vos informations lorsque vous utilisez notre application destinée au calcul du RIP pour les comptes CCP.\n\nNous respectons votre vie privée et nous nous engageons à protéger vos données personnelles.\n\nAvertissement : cette application n\'est pas affiliée à Algérie Poste ni à un organisme gouvernemental. Elle est uniquement un outil destiné à assister les utilisateurs.';
    }
  }

  String _getObjectiveTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Application purpose';
      case 'ar':
        return 'الغرض من التطبيق';
      default:
        return 'Objectif de l\'application';
    }
  }

  String _getObjectiveContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• Help convert CCP to RIP\n• Automatically calculate corresponding keys\n• Allow local or cloud backup (user\'s choice)';
      case 'ar':
        return '• المساعدة في تحويل CCP إلى RIP\n• حساب المفاتيح المقابلة تلقائيًا\n• السماح بالنسخ الاحتياطي المحلي أو السحابي (اختيار المستخدم)';
      default:
        return '• Aider à convertir le CCP en RIP\n• Calculer automatiquement les clés correspondantes\n• Permettre la sauvegarde locale ou cloud (au choix de l\'utilisateur)';
    }
  }

  String _getDataCollectionTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Data collection and use';
      case 'ar':
        return 'جمع البيانات واستخدامها';
      default:
        return 'Collecte et utilisation des données';
    }
  }

  String _getDataCollectionContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• Local data only: CCP information you enter is stored exclusively on your device\n• Optional Google Drive backup: if activated, your data is encrypted and stored in your personal Google Drive account. We have no access to it\n• No hidden analytics: we do not collect personal data for undeclared statistical purposes\n• QR Scanner: camera access is only used to read QR codes containing CCP information\n• Log Data: in case of error, some technical information may be collected automatically by integrated third-party services';
      case 'ar':
        return '• البيانات المحلية فقط: معلومات CCP التي تدخلها مخزنة حصريًا على جهازك\n• النسخ الاحتياطي الاختياري لـ Google Drive: إذا تم تفعيله، يتم تشفير بياناتك وتخزينها في حساب Google Drive الشخصي الخاص بك. ليس لدينا أي وصول إليها\n• لا توجد تحليلات مخفية: نحن لا نجمع البيانات الشخصية لأغراض إحصائية غير معلنة\n• ماسح QR: يُستخدم الوصول إلى الكاميرا فقط لقراءة رموز QR التي تحتوي على معلومات CCP\n• بيانات السجل: في حالة حدوث خطأ، قد يتم جمع بعض المعلومات التقنية تلقائيًا بواسطة خدمات الطرف الثالث المدمجة';
      default:
        return '• Données locales uniquement : les informations CCP que vous saisissez sont stockées exclusivement sur votre appareil\n• Sauvegarde optionnelle Google Drive : si vous l\'activez, vos données sont chiffrées et stockées dans votre compte Google Drive personnel. Nous n\'y avons aucun accès\n• Aucune analyse cachée : nous ne collectons pas de données personnelles à des fins statistiques non déclarées\n• Scanner QR : l\'accès à la caméra sert uniquement à lire des QR codes contenant des informations CCP\n• Données de journal (Log Data) : en cas d\'erreur, certaines informations techniques peuvent être collectées automatiquement par des services tiers intégrés';
    }
  }

  String _getAdsTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Google AdMob advertising';
      case 'ar':
        return 'إعلانات Google AdMob';
      default:
        return 'Publicités Google AdMob';
    }
  }

  String _getAdsContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'This application uses Google AdMob to display advertisements. Google may collect certain anonymous information for advertising purposes, including:\n\n• Device advertising identifier\n• Device information (model, operating system)\n• Approximate location data\n• Interactions with advertisements\n\nThis data is processed by Google according to their privacy policy. Advertisements may come from Google\'s third-party advertising partners, who may collect anonymous information to personalize ads. No personal data from the application is shared with these partners.\n\nLearn more: Google AdMob Policy | Advertising Settings';
      case 'ar':
        return 'يستخدم هذا التطبيق Google AdMob لعرض الإعلانات. قد تجمع Google معلومات مجهولة معينة لأغراض إعلانية، بما في ذلك:\n\n• معرف الإعلان للجهاز\n• معلومات الجهاز (الطراز، نظام التشغيل)\n• بيانات الموقع التقريبية\n• التفاعلات مع الإعلانات\n\nتتم معالجة هذه البيانات من قبل Google وفقًا لسياسة الخصوصية الخاصة بهم. قد تأتي الإعلانات من شركاء الإعلان التابعين لطرف ثالث من Google، والذين قد يجمعون معلومات مجهولة لتخصيص الإعلانات. لا يتم مشاركة أي بيانات شخصية من التطبيق مع هؤلاء الشركاء.\n\nتعرف على المزيد: سياسة Google AdMob | إعدادات الإعلان';
      default:
        return 'Cette application utilise Google AdMob pour afficher des publicités. Google peut collecter certaines informations anonymes à des fins publicitaires, notamment :\n\n• Identifiant publicitaire de l\'appareil\n• Informations sur l\'appareil (modèle, système d\'exploitation)\n• Données de localisation approximative\n• Interactions avec les publicités\n\nCes données sont traitées par Google selon leur politique de confidentialité. Les publicités peuvent provenir de partenaires publicitaires tiers de Google, qui peuvent collecter des informations anonymes pour personnaliser les annonces. Aucune donnée personnelle de l\'application n\'est partagée avec ces partenaires.\n\nEn savoir plus : Politique Google AdMob | Paramètres publicitaires';
    }
  }

  String _getThirdPartyTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Third-party services used';
      case 'ar':
        return 'الخدمات الخارجية المستخدمة';
      default:
        return 'Services tiers utilisés';
    }
  }

  String _getThirdPartyContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'The application may use third-party services that may collect technical data:\n\n• Google Play Services\n• Firebase Analytics (anonymized statistics)\n• AdMob (advertising)\n• Google Drive API (optional data backup)\n• Google OAuth (secure authentication)';
      case 'ar':
        return 'قد يستخدم التطبيق خدمات طرف ثالث قد تجمع بيانات تقنية:\n\n• خدمات Google Play\n• Firebase Analytics (إحصائيات مجهولة)\n• AdMob (إعلانات)\n• Google Drive API (نسخ احتياطي اختياري للبيانات)\n• Google OAuth (مصادقة آمنة)';
      default:
        return 'L\'application peut faire appel à des services tiers susceptibles de collecter des données techniques :\n\n• Google Play Services\n• Firebase Analytics (statistiques anonymisées)\n• AdMob (publicités)\n• Google Drive API (sauvegarde optionnelle des données)\n• Google OAuth (authentification sécurisée)';
    }
  }

  String _getPermissionsTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Required permissions';
      case 'ar':
        return 'الأذونات المطلوبة';
      default:
        return 'Permissions nécessaires';
    }
  }

  String _getPermissionsContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• CAMERA – Scan RIP/CCP QR codes\n• INTERNET – Google Drive backup and advertising display\n• STORAGE – Secure local backup';
      case 'ar':
        return '• الكاميرا – مسح رموز QR لـ RIP/CCP\n• الإنترنت – النسخ الاحتياطي لـ Google Drive وعرض الإعلانات\n• التخزين – النسخ الاحتياطي المحلي الآمن';
      default:
        return '• CAMERA – Scanner les QR codes RIP/CCP\n• INTERNET – Sauvegarde Google Drive et affichage publicitaire\n• STOCKAGE – Sauvegarde locale sécurisée';
    }
  }

  String _getSecurityTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Data security';
      case 'ar':
        return 'أمان البيانات';
      default:
        return 'Sécurité des données';
    }
  }

  String _getSecurityContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• Local encryption: your data is protected on the device\n• Secure backups: Google Drive backup benefits from Google\'s infrastructure and authentication\n• No unauthorized transmission: your data only leaves the device with your explicit action';
      case 'ar':
        return '• التشفير المحلي: بياناتك محمية على الجهاز\n• النسخ الاحتياطية الآمنة: النسخ الاحتياطي لـ Google Drive يستفيد من البنية التحتية والمصادقة من Google\n• لا يوجد نقل غير مصرح به: بياناتك لا تغادر الجهاز إلا بإجراء صريح منك';
      default:
        return '• Chiffrement local : vos données sont protégées sur l\'appareil\n• Sauvegardes sécurisées : la sauvegarde Google Drive bénéficie de l\'infrastructure et de l\'authentification Google\n• Aucune transmission non autorisée : vos données ne quittent l\'appareil qu\'avec votre action explicite';
    }
  }

  String _getRetentionTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Data retention';
      case 'ar':
        return 'الاحتفاظ بالبيانات';
      default:
        return 'Conservation des données';
    }
  }

  String _getRetentionContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• Local data: retained as long as the app remains installed or until manual deletion\n• Google Drive backups: retained according to Google Drive rules\n• OAuth tokens: stored securely and regenerated automatically';
      case 'ar':
        return '• البيانات المحلية: محفوظة طالما بقي التطبيق مثبتًا أو حتى الحذف اليدوي\n• نسخ Google Drive الاحتياطية: محفوظة وفقًا لقواعد Google Drive\n• رموز OAuth: مخزنة بشكل آمن ومُجددة تلقائيًا';
      default:
        return '• Données locales : conservées tant que l\'app reste installée ou jusqu\'à suppression manuelle\n• Sauvegardes Google Drive : conservées selon les règles de Google Drive\n• Jetons OAuth : stockés de façon sécurisée et régénérés automatiquement';
    }
  }

  String _getUserRightsTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'User rights';
      case 'ar':
        return 'حقوق المستخدم';
      default:
        return 'Droits des utilisateurs';
    }
  }

  String _getUserRightsContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '• Access your locally stored data\n• Delete your local data at any time\n• Revoke Google Drive access from your Google account\n• Export/share your data via integrated functions';
      case 'ar':
        return '• الوصول إلى بياناتك المخزنة محليًا\n• حذف بياناتك المحلية في أي وقت\n• إلغاء الوصول إلى Google Drive من حساب Google الخاص بك\n• تصدير/مشاركة بياناتك عبر الوظائف المدمجة';
      default:
        return '• Accéder à vos données stockées localement\n• Supprimer vos données locales à tout moment\n• Révoquer l\'accès à Google Drive depuis votre compte Google\n• Exporter/partager vos données via les fonctions intégrées';
    }
  }

  String _getCookiesTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Cookies';
      case 'ar':
        return 'ملفات تعريف الارتباط';
      default:
        return 'Cookies';
    }
  }

  String _getCookiesContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'The application does not directly use cookies. However, certain third-party services (e.g. AdMob, Firebase) may use cookies to improve their services. You can accept or refuse these cookies via your device settings.';
      case 'ar':
        return 'لا يستخدم التطبيق ملفات تعريف الارتباط مباشرة. ومع ذلك، قد تستخدم بعض خدمات الطرف الثالث (مثل AdMob، Firebase) ملفات تعريف الارتباط لتحسين خدماتها. يمكنك قبول أو رفض ملفات تعريف الارتباط هذه عبر إعدادات جهازك.';
      default:
        return 'L\'application n\'utilise pas directement de cookies. Cependant, certains services tiers (par ex. AdMob, Firebase) peuvent utiliser des cookies pour améliorer leurs services. Vous pouvez accepter ou refuser ces cookies via les réglages de votre appareil.';
    }
  }

  String _getLinksTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Links to other sites';
      case 'ar':
        return 'روابط لمواقع أخرى';
      default:
        return 'Liens vers d\'autres sites';
    }
  }

  String _getLinksContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'The application may contain links to external sites (e.g. Google). We are not responsible for their privacy policies or content.';
      case 'ar':
        return 'قد يحتوي التطبيق على روابط لمواقع خارجية (مثل Google). نحن لسنا مسؤولين عن سياسات الخصوصية أو المحتوى الخاص بهم.';
      default:
        return 'L\'application peut contenir des liens vers des sites externes (ex. Google). Nous ne sommes pas responsables de leurs politiques de confidentialité ni de leur contenu.';
    }
  }

  String _getChildrenTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Children\'s privacy';
      case 'ar':
        return 'خصوصية الأطفال';
      default:
        return 'Confidentialité des enfants';
    }
  }

  String _getChildrenContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'This application is not intended for persons under 13 years of age. We do not knowingly collect their personal information. If a parent or guardian finds that a child has provided data, they can contact us for immediate deletion.';
      case 'ar':
        return 'هذا التطبيق غير مخصص للأشخاص الذين تقل أعمارهم عن 13 عامًا. نحن لا نجمع معلوماتهم الشخصية عن قصد. إذا وجد أحد الوالدين أو الوصي أن طفلاً قد قدم بيانات، فيمكنه الاتصال بنا للحذف الفوري.';
      default:
        return 'Cette application ne s\'adresse pas aux personnes de moins de 13 ans. Nous ne collectons pas volontairement leurs informations personnelles. Si un parent ou tuteur constate qu\'un enfant a fourni des données, il peut nous contacter pour suppression immédiate.';
    }
  }

  String _getChangesTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Policy changes';
      case 'ar':
        return 'تغييرات السياسة';
      default:
        return 'Modifications de cette politique';
    }
  }

  String _getChangesContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'We may update our privacy policy from time to time. Therefore, we recommend that you regularly consult this page to be aware of any changes. Any updates will be posted on this page to inform you.';
      case 'ar':
        return 'قد نقوم بتحديث سياسة الخصوصية الخاصة بنا من وقت لآخر. لذلك، نوصيك بمراجعة هذه الصفحة بانتظام لتكون على علم بأي تغييرات. سيتم نشر أي تحديثات على هذه الصفحة لإعلامك.';
      default:
        return 'Nous pouvons mettre à jour notre politique de confidentialité de temps à autre. Par conséquent, nous vous recommandons de consulter régulièrement cette page afin de prendre connaissance de toute modification. Toute mise à jour sera publiée sur cette page afin de vous en informer.';
    }
  }

  String _getContactTitle(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Contact';
      case 'ar':
        return 'الاتصال';
      default:
        return 'Contact';
    }
  }

  String _getContactContent(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'For any questions or concerns regarding privacy: <EMAIL>';
      case 'ar':
        return 'لأي أسئلة أو مخاوف تتعلق بالخصوصية: <EMAIL>';
      default:
        return 'Pour toute question ou préoccupation relative à la confidentialité : <EMAIL>';
    }
  }

  String _getPolicyLinkText(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'Read the complete policy online';
      case 'ar':
        return 'اقرأ السياسة الكاملة عبر الإنترنت';
      default:
        return 'Lire la politique complète en ligne';
    }
  }
}
