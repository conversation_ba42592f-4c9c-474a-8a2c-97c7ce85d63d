import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';

import '../models/ccp_account.dart';
import '../providers/ccp_accounts_provider.dart';
// import '../utils/ccp_calculator.dart'; // Not directly used here for calculation

class SaveAccountScreen extends StatefulWidget {
  final String ccpNumber;
  final int ccpKey;
  final int ripKey;
  final String ripCode;

  const SaveAccountScreen({
    super.key,
    required this.ccpNumber,
    required this.ccpKey,
    required this.ripKey,
    required this.ripCode,
  });

  @override
  State<SaveAccountScreen> createState() => _SaveAccountScreenState();
}

class _SaveAccountScreenState extends State<SaveAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _ownerNameController = TextEditingController();
  bool _isSaving = false;

  @override
  void dispose() {
    _ownerNameController.dispose();
    super.dispose();
  }

  Future<bool> _checkIfAccountExists() async {
    if (!mounted) return false;
    final provider = context.read<CCPAccountsProvider>();
    // Ensure accounts are loaded if not already
    // This might be redundant if provider loads on init or CCPForm already did.
    // await provider.loadAccounts();
    final accounts = provider.accounts;
    final cleanRipCode = widget.ripCode.replaceAll(' ', '');
    return accounts.any((account) => account.ripCode.replaceAll(' ', '') == cleanRipCode);
  }

  void _saveAccount() async {
    final loc = AppLocalizations.of(context);
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });

      try {
        final accountExists = await _checkIfAccountExists();
        if (accountExists) {
          if (!mounted) return;
          final errorColor = const Color(0xFFD32F2F);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      loc.ccpFormAccountExistsError, // Reusing key
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              backgroundColor: errorColor,
              duration: const Duration(seconds: 3),
            ),
          );
          setState(() { _isSaving = false; });
          return;
        }

        final account = CCPAccount(
          ccpNumber: widget.ccpNumber,
          ownerName: _ownerNameController.text.trim(),
          ccpKey: widget.ccpKey,
          ripKey: widget.ripKey,
          ripCode: widget.ripCode,
        );

        if (!mounted) return;
        final provider = context.read<CCPAccountsProvider>();
        await provider.addAccount(account);

        // Récupérer le compte nouvellement créé avec son ID
        final accounts = provider.accounts;
        final newAccount = accounts.firstWhere(
          (a) => a.ccpNumber == widget.ccpNumber,
          orElse: () => account,
        );

        if (!mounted) return;
        final successColor = const Color(0xFF4CAF50);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(loc.saveAccountSuccessMessage),
              ],
            ),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: successColor,
            duration: const Duration(seconds: 2),
          ),
        );

        if (!mounted) return;

        // Utiliser Future.microtask pour éviter les problèmes de navigation
        Future.microtask(() {
          if (mounted) {
            // Pop SaveAccountScreen and return the ID of the newly created account
            Navigator.of(context).pop(newAccount.id);
            // CCPForm will handle clearing its fields and navigating to the accounts tab.
          }
        });

      } catch (e) {
        if (!mounted) return;
        String errorMessage = e.toString();
        // Check if the error message is the one for existing account (less likely here now)
        if (errorMessage.contains('Un compte avec ce numéro CCP existe déjà')) { // Hardcoded string, should be avoided
          errorMessage = loc.ccpFormAccountExistsError;
        } else {
          errorMessage = '${loc.saveAccountGenericErrorPrefix}$errorMessage';
        }
        final errorColor = const Color(0xFFD32F2F);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      errorMessage,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: errorColor,
            duration: const Duration(seconds: 3),
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
        }
      }
    }
  }

  Widget _buildInfoItem({
    required BuildContext context,
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: color,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
              Text(
                value,
                textDirection: TextDirection.ltr, // Ensure LTR for numerical values
                style: TextStyle(
                  fontSize: 15,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF3F51B5);
    final surfaceColor = const Color(0xFFF5F5F5);
    final textColor = const Color(0xFF212121);
    final accentYellow = const Color(0xFFFFD600);
    final loc = AppLocalizations.of(context);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          // Utiliser un simple Navigator.pop pour éviter les problèmes
          Navigator.of(context).pop(false);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            loc.saveAccountScreenTitle,
            overflow: TextOverflow.visible, // Permet au texte de déborder si nécessaire
            style: const TextStyle(fontSize: 16), // Taille de police réduite
          ),
          titleSpacing: 0, // Réduit l'espace entre le titre et les actions
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.15),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 0,
                  margin: EdgeInsets.zero,
                  color: surfaceColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: primaryColor.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: accentYellow.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.account_balance_wallet,
                                size: 28,
                                color: textColor,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                loc.verifyRipInfoTitle, // Reusing key
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: textColor,
                                ),
                                overflow: TextOverflow.visible,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        child: Divider(height: 1),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoItem(
                              context: context,
                              label: loc.verifyRipInfoCcpNumberLabel, // Reusing key
                              value: widget.ccpNumber,
                              icon: Icons.credit_card_outlined,
                              color: primaryColor,
                            ),
                            const SizedBox(height: 12),
                            _buildInfoItem(
                              context: context,
                              label: loc.verifyRipInfoCcpKeyLabel, // Reusing key
                              value: widget.ccpKey.toString().padLeft(2, '0'),
                              icon: Icons.key_outlined,
                              color: primaryColor,
                            ),
                            const SizedBox(height: 12),
                            _buildInfoItem(
                              context: context,
                              label: loc.verifyRipInfoRipKeyLabel, // Reusing key
                              value: widget.ripKey.toString().padLeft(2, '0'),
                              icon: Icons.vpn_key_outlined,
                              color: primaryColor,
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                Icon(
                                  Icons.account_balance_wallet_outlined,
                                  size: 18,
                                  color: primaryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  loc.verifyRipFullRipLabel, // Reusing key
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: primaryColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 20),
                              decoration: BoxDecoration(
                                color: accentYellow.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: accentYellow,
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: accentYellow.withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      widget.ripCode,
                                      textDirection: TextDirection.ltr, // Ensure LTR
                                      style: TextStyle(
                                        letterSpacing: 0.8,
                                        fontFamily: 'monospace',
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.visible,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.15),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 0,
                  margin: EdgeInsets.zero,
                  color: surfaceColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: primaryColor.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Titre "Propriétaire" avec icône
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: accentYellow.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.person_outline,
                                size: 24,
                                color: textColor,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                "Propriétaire",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: textColor,
                                ),
                                overflow: TextOverflow.visible,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: TextFormField(
                              controller: _ownerNameController,
                              decoration: InputDecoration(
                                hintText: loc.saveAccountOwnerNameHint,
                                filled: true,
                                fillColor: accentYellow.withOpacity(0.1),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: accentYellow.withOpacity(0.3),
                                    width: 1.0,
                                  ),
                                ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: accentYellow,
                                width: 1.5,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                            prefixIcon: Icon(
                              Icons.person_outline,
                              color: primaryColor,
                            ),
                          ),
                          style: const TextStyle(
                            fontWeight: FontWeight.normal,
                            fontSize: 15,
                          ),
                          textCapitalization: TextCapitalization.words,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return loc.saveAccountOwnerNameValidation;
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: ElevatedButton(
                  onPressed: _isSaving ? null : _saveAccount,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: _isSaving
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text( // TODO: Localize "ENREGISTREMENT..."
                              'ENREGISTREMENT...',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.save,
                              size: 22,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              loc.savedAccountsDialogSave, // Reusing key
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
         ),
        ),
      ),
    );
  }
}
