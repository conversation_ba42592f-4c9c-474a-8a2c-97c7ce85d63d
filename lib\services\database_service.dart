import 'dart:async';
import 'dart:io';

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

import '../models/ccp_account.dart';

/// Service class for handling database operations
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();

  factory DatabaseService() => _instance;

  DatabaseService._internal();

  static Database? _database;

  /// Database name
  static const String _databaseName = 'ccp_rip_database.db';

  /// Database version
  static const int _databaseVersion = 1;

  /// Table name for CCP accounts
  static const String tableCCPAccounts = 'ccp_accounts';

  /// Get the database instance
  Future<Database> get database async {
    if (_database != null) return _database!;

    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database
  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  /// Create the database tables
  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableCCPAccounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ccpNumber TEXT NOT NULL,
        ownerName TEXT NOT NULL,
        ccpKey INTEGER NOT NULL,
        ripKey INTEGER NOT NULL,
        ripCode TEXT NOT NULL,
        dateModified TEXT NOT NULL
      )
    ''');
  }

  /// Vérifie si un compte RIP existe déjà dans la base de données
  Future<bool> ripAccountExists(String ripCode) async {
    // Nettoyer le code RIP (supprimer les espaces)
    final cleanRipCode = ripCode.replaceAll(' ', '');

    Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableCCPAccounts,
    );

    // Vérifier si un compte avec ce code RIP existe déjà
    for (var map in maps) {
      final existingRipCode = map['ripCode'].toString().replaceAll(' ', '');
      if (existingRipCode == cleanRipCode) {
        return true;
      }
    }

    return false;
  }

  /// Insert a new CCP account into the database
  Future<int> insertCCPAccount(CCPAccount account) async {
    // Vérifier si le compte existe déjà
    final exists = await ripAccountExists(account.ripCode);
    if (exists) {
      throw Exception('Un compte avec ce code RIP existe déjà');
    }

    Database db = await database;
    return await db.insert(
      tableCCPAccounts,
      account.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing CCP account in the database
  Future<int> updateCCPAccount(CCPAccount account) async {
    Database db = await database;
    return await db.update(
      tableCCPAccounts,
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  /// Delete a CCP account from the database
  Future<int> deleteCCPAccount(int id) async {
    Database db = await database;
    return await db.delete(
      tableCCPAccounts,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Delete all CCP accounts from the database
  Future<int> deleteAllCCPAccounts() async {
    Database db = await database;
    return await db.delete(tableCCPAccounts);
  }

  /// Get all CCP accounts from the database
  Future<List<CCPAccount>> getAllCCPAccounts() async {
    Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(tableCCPAccounts);

    return List.generate(maps.length, (i) {
      return CCPAccount.fromMap(maps[i]);
    });
  }

  /// Get a CCP account by its ID
  Future<CCPAccount?> getCCPAccountById(int id) async {
    Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableCCPAccounts,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CCPAccount.fromMap(maps.first);
    }

    return null;
  }

  /// Search for CCP accounts by owner name or CCP number
  Future<List<CCPAccount>> searchCCPAccounts(String query) async {
    Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableCCPAccounts,
      where: 'ownerName LIKE ? OR ccpNumber LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );

    return List.generate(maps.length, (i) {
      return CCPAccount.fromMap(maps[i]);
    });
  }

  /// Export all CCP accounts as a JSON string
  Future<String> exportCCPAccountsAsJson() async {
    final accounts = await getAllCCPAccounts();
    final List<Map<String, dynamic>> jsonList = accounts.map((account) => account.toMap()).toList();
    return jsonList.toString();
  }

  /// Import CCP accounts from a JSON string
  Future<int> importCCPAccountsFromJson(String jsonString) async {
    // Parse the JSON string and convert it to a list of maps
    // This is a simplified implementation and might need more robust parsing
    final String cleanedJson = jsonString.replaceAll(RegExp(r'[\[\]]'), '');
    final List<String> accountStrings = cleanedJson.split('}, {');

    int importedCount = 0;

    for (String accountString in accountStrings) {
      try {
        // Clean up the string
        String cleaned = accountString.replaceAll('{', '').replaceAll('}', '');

        // Split into key-value pairs
        List<String> pairs = cleaned.split(', ');

        // Create a map from the pairs
        Map<String, dynamic> map = {};
        for (String pair in pairs) {
          List<String> keyValue = pair.split(': ');
          if (keyValue.length == 2) {
            String key = keyValue[0].replaceAll(RegExp("['\"]+"), '');
            String value = keyValue[1].replaceAll(RegExp("['\"]+"), '');

            // Convert values to appropriate types
            if (key == 'id' || key == 'ccpKey' || key == 'ripKey') {
              map[key] = int.tryParse(value) ?? 0;
            } else {
              map[key] = value;
            }
          }
        }

        // Create a CCPAccount from the map and insert it
        if (map.containsKey('ccpNumber') && map.containsKey('ownerName')) {
          final account = CCPAccount.fromMap(map);
          await insertCCPAccount(account);
          importedCount++;
        }
      } catch (e) {
        print('Error importing account: $e');
      }
    }

    return importedCount;
  }
}
