import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

import 'database_service.dart';
import '../l10n/app_localizations.dart';
import '../widgets/custom_button.dart';

/// Service class for handling import and export operations
class ExportService {
  final DatabaseService _databaseService = DatabaseService();

  /// Export all CCP accounts to a file and share it
  Future<void> exportAndShareAccounts() async {
    try {
      // Get the JSON data
      final jsonData = await _databaseService.exportCCPAccountsAsJson();

      // Create a temporary file
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/ccp_accounts_export.json');

      // Write the JSON data to the file
      await file.writeAsString(jsonData);

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'CCP Accounts Export',
        text: 'Here are my exported CCP accounts',
      );
    } catch (e) {
      print('Error exporting accounts: $e');
      rethrow;
    }
  }

  /// Import CCP accounts from a string
  Future<int> importAccountsFromString(String jsonData) async {
    try {
      // Import the data
      return await _databaseService.importCCPAccountsFromJson(jsonData);
    } catch (e) {
      print('Error importing accounts: $e');
      rethrow;
    }
  }

  /// Import CCP accounts from a file
  Future<int> importAccountsFromFile() async {
    try {
      // Pick a JSON file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        // Read the file content
        final file = File(result.files.single.path!);
        final jsonData = await file.readAsString();

        // Import the data
        return await _databaseService.importCCPAccountsFromJson(jsonData);
      } else {
        // User canceled the picker
        return 0;
      }
    } catch (e) {
      print('Error importing accounts from file: $e');
      rethrow;
    }
  }

  /// Show a dialog to import accounts with options
  Future<int> showImportDialog(BuildContext context) async {
    final loc = AppLocalizations.of(context);
    int importedCount = 0;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(loc.localBackupImport ?? 'Importer des comptes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(loc.localBackupImportDescription ?? 'Restaurez vos comptes à partir d\'un fichier de sauvegarde précédemment créé.'),
            const SizedBox(height: 20),

            // Bouton unique: Sélectionner un fichier
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                onPressed: () async {
                  try {
                    importedCount = await importAccountsFromFile();
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  } catch (e) {
                    // L'erreur sera gérée par l'appelant
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                    rethrow;
                  }
                },
                text: loc.localBackupImportButton ?? 'Sélectionner un fichier',
                icon: Icons.upload_file,
                type: ButtonType.import,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(loc.savedAccountsDialogCancel),
          ),
        ],
      ),
    );

    return importedCount;
  }


}
