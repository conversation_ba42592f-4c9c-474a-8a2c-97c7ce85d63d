import 'package:flutter/material.dart';

class AppStateProvider extends ChangeNotifier with WidgetsBindingObserver {
  // État de l'application
  AppLifecycleState _appLifecycleState = AppLifecycleState.resumed;
  bool _isAppInBackground = false;
  
  // Données temporaires à conserver
  String? _tempCcpInput;
  String? _tempRipInput;
  String? _tempCalculatedRip;
  bool _hasTemporaryData = false;

  AppStateProvider() {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Getters
  AppLifecycleState get appLifecycleState => _appLifecycleState;
  bool get isAppInBackground => _isAppInBackground;
  String? get tempCcpInput => _tempCcpInput;
  String? get tempRipInput => _tempRipInput;
  String? get tempCalculatedRip => _tempCalculatedRip;
  bool get hasTemporaryData => _hasTemporaryData;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _appLifecycleState = state;

    switch (state) {
      case AppLifecycleState.resumed:
        // App est revenue au premier plan
        _isAppInBackground = false;
        debugPrint('App resumed - données temporaires conservées');
        break;

      case AppLifecycleState.paused:
        // App est mise en arrière-plan
        _isAppInBackground = true;
        debugPrint('App paused - conservation des données temporaires');
        break;

      case AppLifecycleState.detached:
        // App est fermée définitivement
        _clearTemporaryData();
        debugPrint('App detached - données temporaires supprimées');
        break;

      case AppLifecycleState.inactive:
        // App est inactive (transition)
        break;

      case AppLifecycleState.hidden:
        // App est cachée
        break;
    }

    // Utiliser WidgetsBinding pour éviter les conflits de dépendances
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Sauvegarder les données temporaires du formulaire CCP
  void saveTempCcpData(String ccpInput, String? calculatedRip) {
    _tempCcpInput = ccpInput;
    _tempCalculatedRip = calculatedRip;
    _hasTemporaryData = ccpInput.isNotEmpty || (calculatedRip?.isNotEmpty ?? false);
    debugPrint('Données CCP temporaires sauvegardées: CCP=$ccpInput, RIP=$calculatedRip');

    // Utiliser WidgetsBinding pour éviter les conflits de dépendances
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Sauvegarder les données temporaires du formulaire RIP
  void saveTempRipData(String ripInput) {
    _tempRipInput = ripInput;
    _hasTemporaryData = ripInput.isNotEmpty || _hasTemporaryData;
    debugPrint('Données RIP temporaires sauvegardées: RIP=$ripInput');

    // Utiliser WidgetsBinding pour éviter les conflits de dépendances
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Restaurer les données CCP
  Map<String, String?> restoreCcpData() {
    return {
      'ccpInput': _tempCcpInput,
      'calculatedRip': _tempCalculatedRip,
    };
  }

  // Restaurer les données RIP
  String? restoreRipData() {
    return _tempRipInput;
  }

  // Effacer les données temporaires
  void _clearTemporaryData() {
    _tempCcpInput = null;
    _tempRipInput = null;
    _tempCalculatedRip = null;
    _hasTemporaryData = false;
    debugPrint('Toutes les données temporaires ont été effacées');

    // Utiliser WidgetsBinding pour éviter les conflits de dépendances
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Effacer manuellement les données temporaires (quand l'utilisateur le souhaite)
  void clearTemporaryData() {
    _clearTemporaryData();
  }

  // Vérifier si l'app revient du background avec des données
  bool shouldRestoreData() {
    return !_isAppInBackground && _hasTemporaryData;
  }

  // Méthode pour empêcher le rechargement complet de l'app
  void preventAppReload() {
    // Cette méthode peut être appelée pour s'assurer que l'état est préservé
    if (_hasTemporaryData) {
      debugPrint('Prévention du rechargement - données temporaires préservées');
    }
  }
}
