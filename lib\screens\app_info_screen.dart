import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Écran d'information sur l'application
class AppInfoScreen extends StatefulWidget {
  const AppInfoScreen({super.key});

  @override
  State<AppInfoScreen> createState() => _AppInfoScreenState();
}

class _AppInfoScreenState extends State<AppInfoScreen> {
  String _version = '';

  @override
  void initState() {
    super.initState();
    _loadVersion();
  }

  Future<void> _loadVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = 'Version ${packageInfo.version}';
      });
    } catch (e) {
      setState(() {
        _version = 'Version 2.2.0';
      });
    }
  }

  // Méthode pour construire un élément de fonctionnalité
  Widget _buildFeatureItem(
    BuildContext context, {
    required String icon,
    required String title,
    required String description,
    List<String>? subItems,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFFDE7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFFEC33).withAlpha(50),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Titre de la fonctionnalité
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                icon,
                style: const TextStyle(
                  fontSize: 18,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),

          // Description (si non vide)
          if (description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 28),
              child: Text(
                description,
                style: const TextStyle(
                  fontSize: 12,
                  height: 1.4,
                ),
              ),
            ),
          ],

          // Sous-éléments (si présents)
          if (subItems != null && subItems.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...subItems.map((item) => Padding(
              padding: const EdgeInsets.only(left: 28, top: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "•",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 10,
                        height: 1.2,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          Localizations.localeOf(context).languageCode == 'ar'
              ? "حول تطبيق CCP RIP DZ"
              : Localizations.localeOf(context).languageCode == 'en'
                  ? "About CCP RIP DZ"
                  : "À propos de CCP RIP DZ",
          overflow: TextOverflow.visible,
          style: const TextStyle(fontSize: 16),
        ),
        titleSpacing: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Logo de l'application
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Image.asset(
                  'assets/images/logo.png',
                  height: 90,
                ),
              ),
            ),

            // Contenu de la page d'information
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withAlpha(25),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Titre
                  Center(
                    child: Text(
                      "📱 ${Localizations.localeOf(context).languageCode == 'ar'
                          ? "حول تطبيق CCP RIP DZ"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "About CCP RIP DZ"
                              : "À propos de CCP RIP DZ"}",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Introduction
                  Text(
                    Localizations.localeOf(context).languageCode == 'ar'
                        ? "مرحبًا بكم في تطبيق CCP RIP DZ!"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Welcome to the CCP RIP DZ app!"
                            : "Bienvenue sur notre application CCP RIP DZ !",
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      height: 1.4,
                    ),
                  ),

                  const SizedBox(height: 6),

                  Text(
                    Localizations.localeOf(context).languageCode == 'ar'
                        ? "يوفر لكم هذا التطبيق وسيلة سهلة وفعالة لإدارة حسابات CCP في الجزائر. إليكم ما يمكنكم القيام به باستخدام هذا التطبيق:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Our app provides a simple and effective solution for managing CCP accounts in Algeria. Here's what you can do with it:"
                            : "Notre application vous offre une solution simple et efficace pour gérer vos comptes CCP en Algérie. Voici ce que vous pouvez faire avec notre outil :",
                    style: const TextStyle(
                      fontSize: 12,
                      height: 1.4,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Fonctionnalités principales
                  Text(
                    "✨ ${Localizations.localeOf(context).languageCode == 'ar'
                        ? "الميزات الرئيسية:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Key Features:"
                            : "Fonctionnalités principales :"}",
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      height: 1.4,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Calcul du RIP
                  _buildFeatureItem(
                    context,
                    icon: "⚙️",
                    title: Localizations.localeOf(context).languageCode == 'ar'
                        ? "حساب رقم RIP:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "RIP Calculation:"
                            : "Calcul du RIP :",
                    description: Localizations.localeOf(context).languageCode == 'ar'
                        ? "احسب بسهولة رقم RIP (البيان البريدي) انطلاقًا من رقم CCP بدون المفتاح."
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Easily calculate the RIP (Postal Identity Statement) from your CCP number without the key."
                            : "Calculez facilement le RIP (Relevé d'Identité Postale) à partir de votre numéro CCP sans clé.",
                  ),

                  const SizedBox(height: 8),

                  // Sauvegarde des comptes
                  _buildFeatureItem(
                    context,
                    icon: "📋",
                    title: Localizations.localeOf(context).languageCode == 'ar'
                        ? "حفظ الحسابات:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Save Accounts:"
                            : "Sauvegarde des comptes :",
                    description: Localizations.localeOf(context).languageCode == 'ar'
                        ? "قم بحفظ الحسابات المُحتسبة للوصول إليها لاحقًا بسهولة."
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Store calculated accounts for quick access later."
                            : "Enregistrez les comptes calculés pour un accès rapide.",
                  ),

                  const SizedBox(height: 8),

                  // Partage simplifié
                  _buildFeatureItem(
                    context,
                    icon: "🔄",
                    title: Localizations.localeOf(context).languageCode == 'ar'
                        ? "مشاركة مبسطة:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Easy Sharing:"
                            : "Partage simplifié :",
                    description: "",
                    subItems: [
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? "نسخ رقم RIP"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "Copy the RIP code"
                              : "Copier le code RIP",
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? "إنشاء رمز QR"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "Generate a QR Code"
                              : "Générer un Code QR",
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? "مشاركة الحساب عبر التطبيقات المختلفة"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "Share the account through your favorite apps"
                              : "Partager le compte via vos applications préférées",
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Vérification du RIP
                  _buildFeatureItem(
                    context,
                    icon: "🛡️",
                    title: Localizations.localeOf(context).languageCode == 'ar'
                        ? "التحقق من صحة RIP:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "RIP Validation:"
                            : "Vérification du RIP :",
                    description: Localizations.localeOf(context).languageCode == 'ar'
                        ? "تحقق من صحة رقم RIP موجود:"
                        : Localizations.localeOf(context).languageCode == 'en'
                            ? "Check the validity of an existing RIP code:"
                            : "Vérifiez la validité d'un RIP existant :",
                    subItems: [
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? "عبر إدخال الرقم يدويًا"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "By entering it manually"
                              : "En saisissant manuellement le code RIP",
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? "أو عن طريق مسح رمز QR"
                          : Localizations.localeOf(context).languageCode == 'en'
                              ? "Or scanning a QR Code"
                              : "Ou en scannant un QR Code",
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Informations supplémentaires
            Center(
              child: Column(
                children: [
                  Text(
                    _version.isEmpty ? 'Version 2.2.0' : _version,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '© 2025 Dzair Empreinte',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
