import 'package:flutter/foundation.dart';

import '../models/ccp_account.dart';
import '../services/database_service.dart';

/// Provider class for managing CCP accounts
class CCPAccountsProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();

  List<CCPAccount> _accounts = [];
  List<CCPAccount> _filteredAccounts = [];
  bool _isSearching = false;

  /// Get all accounts
  List<CCPAccount> get accounts => _accounts;

  /// Get filtered accounts (for search)
  List<CCPAccount> get filteredAccounts => _isSearching ? _filteredAccounts : _accounts;

  /// Check if a search is active
  bool get isSearching => _isSearching;

  /// Constructor
  CCPAccountsProvider() {
    loadAccounts();
  }

  /// Load all accounts from the database
  Future<void> loadAccounts() async {
    _accounts = await _databaseService.getAllCCPAccounts();
    _accounts.sort((a, b) => b.dateModified.compareTo(a.dateModified));
    notifyListeners();
  }

  /// Add a new account
  Future<void> addAccount(CCPAccount account) async {
    try {
      // Vérifier d'abord si un compte avec ce code RIP existe déjà
      final cleanRipCode = account.ripCode.replaceAll(' ', '');
      final exists = _accounts.any((a) => a.ripCode.replaceAll(' ', '') == cleanRipCode);
      if (exists) {
        throw Exception('Un compte avec ce code RIP existe déjà');
      }

      // Insérer le compte dans la base de données
      final id = await _databaseService.insertCCPAccount(account);
      final newAccount = account.copyWith(id: id);

      // Ajouter le compte à la liste en mémoire
      _accounts.insert(0, newAccount);
      notifyListeners();
    } catch (e) {
      // Relancer l'exception pour que l'appelant puisse la gérer
      rethrow;
    }
  }

  /// Update an existing account
  Future<void> updateAccount(CCPAccount account) async {
    await _databaseService.updateCCPAccount(account);
    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index != -1) {
      _accounts[index] = account;

      // Also update in filtered accounts if needed
      if (_isSearching) {
        final filteredIndex = _filteredAccounts.indexWhere((a) => a.id == account.id);
        if (filteredIndex != -1) {
          _filteredAccounts[filteredIndex] = account;
        }
      }

      notifyListeners();
    }
  }

  /// Delete an account
  Future<void> deleteAccount(int id) async {
    await _databaseService.deleteCCPAccount(id);
    _accounts.removeWhere((account) => account.id == id);

    // Also remove from filtered accounts if needed
    if (_isSearching) {
      _filteredAccounts.removeWhere((account) => account.id == id);
    }

    notifyListeners();
  }

  /// Search for accounts by name or CCP number
  void searchAccounts(String query) {
    if (query.isEmpty) {
      _isSearching = false;
      _filteredAccounts = [];
    } else {
      _isSearching = true;
      _filteredAccounts = _accounts.where((account) {
        return account.ownerName.toLowerCase().contains(query.toLowerCase()) ||
               account.ccpNumber.contains(query);
      }).toList();
    }
    notifyListeners();
  }

  /// Clear the search
  void clearSearch() {
    _isSearching = false;
    _filteredAccounts = [];
    notifyListeners();
  }

  /// Replace all accounts (used for cloud restore)
  Future<void> replaceAllAccounts(List<CCPAccount> newAccounts) async {
    try {
      // Delete all existing accounts
      await _databaseService.deleteAllCCPAccounts();

      // Insert all new accounts
      for (final account in newAccounts) {
        await _databaseService.insertCCPAccount(account);
      }

      // Reload accounts from database
      await loadAccounts();
    } catch (e) {
      rethrow;
    }
  }
}
