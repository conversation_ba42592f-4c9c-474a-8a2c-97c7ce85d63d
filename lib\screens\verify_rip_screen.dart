import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';
import '../providers/app_state_provider.dart';
import 'package:flutter/services.dart';
import '../l10n/app_localizations.dart';
import '../utils/rip_validator.dart';
import '../widgets/app_drawer.dart';
import 'rip_scanner_screen.dart';
import 'main_screen.dart';
import '../providers/ccp_accounts_provider.dart';
import 'save_account_screen.dart';

class VerifyRIPScreen extends StatefulWidget {
  const VerifyRIPScreen({super.key});

  @override
  State<VerifyRIPScreen> createState() => _VerifyRIPScreenState();
}

class _VerifyRIPScreenState extends State<VerifyRIPScreen> {
  String _getLanguageDisplay(Locale? locale) {
    final languageCode = locale?.languageCode ?? 'en';
    switch (languageCode) {
      case 'ar':
        return 'ع';
      case 'fr':
        return 'Fr';
      default:
        return 'En';
    }
  }
  final _formKey = GlobalKey<FormState>();
  final _ripController = TextEditingController();
  final _ripFocusNode = FocusNode();
  final _scrollController = ScrollController();

  bool? _isValid;
  Map<String, dynamic>? _ripComponents;

  @override
  void initState() {
    super.initState();

    // Restaurer les données temporaires si l'app revient du background
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreTemporaryData();
    });
  }

  @override
  void dispose() {
    // Sauvegarder les données temporaires avant de disposer
    _saveTemporaryData();

    _ripController.dispose();
    _ripFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Restaurer les données temporaires
  void _restoreTemporaryData() {
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    if (appStateProvider.shouldRestoreData()) {
      final ripData = appStateProvider.restoreRipData();
      if (ripData != null && ripData.isNotEmpty) {
        _ripController.text = ripData;
        _verifyRIP();
        debugPrint('Données RIP restaurées: $ripData');
      }
    }
  }

  // Sauvegarder les données temporaires
  void _saveTemporaryData() {
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    appStateProvider.saveTempRipData(_ripController.text);
  }

  void _verifyRIP() {
    if (_formKey.currentState!.validate()) {
      final ripCode = _ripController.text.trim();
      final components = RIPValidator.extractRIPComponents(ripCode);
      final isValid = components['isValid'] as bool;
      setState(() {
        _isValid = isValid;
        _ripComponents = components;
      });

      // Sauvegarder automatiquement les données temporaires
      _saveTemporaryData();

      // Si le RIP est valide, scroll automatiquement pour afficher toute la carte
      if (isValid) {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted && _scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        });
      }
    }
  }

  Future<void> _pasteFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null && clipboardData.text != null) {
      final cleanText = clipboardData.text!.replaceAll(' ', '');
      _ripController.text = cleanText;
      _verifyRIP();
    }
  }

  Future<void> _openRipScanner() async {
    final scannedRip = await Navigator.push<String>(
      context,
      MaterialPageRoute(builder: (context) => const RipScannerScreen()),
    );
    if (scannedRip != null && scannedRip.isNotEmpty) {
      final cleanScannedRip = scannedRip.replaceAll(' ', '');
      _ripController.text = cleanScannedRip;
      _verifyRIP();

      // Pas de focus automatique - l'utilisateur doit cliquer manuellement
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final loc = AppLocalizations.of(context);

    return Scaffold(
      drawer: const AppDrawer(),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          loc.verifyRipScreenTitle,
          overflow: TextOverflow.visible, // Permet au texte de déborder si nécessaire
          style: const TextStyle(fontSize: 16), // Taille de police réduite
        ),
        titleSpacing: 0, // Réduit l'espace entre le titre et les actions
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code_scanner_outlined),
            tooltip: loc.verifyRipScannerTooltip,
            onPressed: _openRipScanner,
            padding: const EdgeInsets.symmetric(horizontal: 4), // Réduit davantage le padding horizontal
            constraints: const BoxConstraints(), // Supprime les contraintes de taille minimale
          ),
          IconButton(
            icon: Consumer<LocaleProvider>(
              builder: (context, localeProvider, _) => Container(
                width: 18, // Taille réduite
                height: 18, // Taille réduite
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.fromBorderSide(
                    BorderSide(color: Colors.white, width: 1.0)
                  ),
                ),
                child: Center(
                  child: Text(
                    _getLanguageDisplay(localeProvider.locale),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 9, // Taille réduite
                      height: 1.0
                    ),
                  ),
                ),
              ),
            ),
            tooltip: loc.changeLanguageTooltip,
            onPressed: () {
              showLanguageSelectionDialog(context);
            },
            padding: const EdgeInsets.symmetric(horizontal: 4), // Réduit le padding horizontal
            constraints: const BoxConstraints(), // Supprime les contraintes de taille minimale
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (!isSmallScreen) ...[
                Center(
                  child: Icon(
                    Icons.verified_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: Text(
                    loc.verifyRipHeaderTitle,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    loc.verifyRipHeaderSubtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
              ],
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _ripController,
                  textDirection: TextDirection.ltr, // Ensure LTR for RIP input
                  decoration: InputDecoration(
                    labelText: loc.verifyRipInputLabel,
                    hintText: loc.verifyRipInputHint,
                    prefixIcon: const Icon(Icons.account_balance_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: const Color(0xFFFFEC33),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.content_paste_outlined),
                      tooltip: loc.verifyRipPasteTooltip,
                      onPressed: _pasteFromClipboard,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 14,
                    letterSpacing: 0.8,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(20),
                  ],
                  onChanged: (_) {
                    if (_isValid != null) {
                      setState(() {
                        _isValid = null;
                        _ripComponents = null;
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return loc.verifyRipValidationEmpty;
                    }
                    if (value.length != 20) {
                      return loc.verifyRipValidationLength;
                    }
                    if (!value.startsWith('00799999')) {
                      return loc.verifyRipValidationPrefix;
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _verifyRIP,
                icon: const Icon(Icons.check_circle_outline),
                label: Text(loc.verifyRipButtonText),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              if (_isValid != null && _ripComponents != null)
                AnimatedOpacity(
                  opacity: 1.0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                  child: _buildVerificationResult(loc),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationResult(AppLocalizations loc) {
    final successColor = const Color(0xFF4CAF50);
    final errorColor = const Color(0xFFD32F2F);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (_isValid! ? successColor : errorColor).withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        color: _isValid!
            ? successColor.withOpacity(0.08)
            : errorColor.withOpacity(0.08),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: _isValid! ? successColor.withOpacity(0.3) : errorColor.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (_isValid! ? successColor : errorColor).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _isValid! ? Icons.check_circle_outline : Icons.error_outline,
                      color: _isValid! ? successColor : errorColor,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _isValid! ? loc.verifyRipResultValid : loc.verifyRipResultInvalid,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _isValid! ? successColor : errorColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _isValid!
                              ? loc.verifyRipResultValidSub
                              : loc.verifyRipResultInvalidSub,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Divider(height: 1),
              ),
              if (_isValid!) ...[
                _buildInfoSection(
                  loc.verifyRipInfoTitle,
                  children: [
                    _buildInfoItem(
                      context,
                      label: loc.verifyRipInfoPrefixLabel,
                      value: _ripComponents!['prefix'],
                      icon: Icons.tag,
                    ),
                    const SizedBox(height: 12),
                    _buildInfoItem(
                      context,
                      label: loc.verifyRipInfoBankCodeLabel,
                      value: _ripComponents!['bankCode'],
                      icon: Icons.account_balance_outlined,
                    ),
                    const SizedBox(height: 12),
                    _buildInfoItem(
                      context,
                      label: loc.verifyRipInfoCcpNumberLabel,
                      value: _ripComponents!['ccpNumber'],
                      icon: Icons.credit_card_outlined,
                    ),
                    const SizedBox(height: 12),
                    _buildInfoItem(
                      context,
                      label: loc.verifyRipInfoCcpKeyLabel,
                      value: _calculateCCPKey(_ripComponents!['ccpNumber']),
                      icon: Icons.key_outlined,
                    ),
                    const SizedBox(height: 12),
                    _buildInfoItem(
                      context,
                      label: loc.verifyRipInfoRipKeyLabel,
                      value: _ripComponents!['ripKey'],
                      icon: Icons.vpn_key_outlined,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                _buildRipCodeDisplay(context, loc),
              ] else if (_ripComponents!.containsKey('errorMessage')) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: errorColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: errorColor.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: errorColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _ripComponents!['errorMessage'],
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: errorColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: errorColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: errorColor.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: errorColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          loc.verifyRipGenericInvalidMessage,
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFFD32F2F),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    String title, {
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: const Color(0xFFFFEC33),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.visible,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoItem(
    BuildContext context, {
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Theme.of(context).colorScheme.secondary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                value,
                textDirection: TextDirection.ltr, // Ensure LTR for numerical values
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRipCodeDisplay(BuildContext context, AppLocalizations loc) {
    final successColor = const Color(0xFF4CAF50);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 18,
              color: successColor,
            ),
            const SizedBox(width: 8),
            Text(
              loc.verifyRipFullRipLabel,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: successColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 20),
          decoration: BoxDecoration(
            color: successColor.withOpacity(0.08),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: successColor.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: successColor.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  _ripComponents!['formattedRipCode'],
                  textDirection: TextDirection.ltr, // Ensure LTR for RIP code display
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    letterSpacing: 0.5,
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.visible,
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Bouton Copier avec icône
                    Expanded(
                      child: Material(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(24),
                        child: InkWell(
                          onTap: () {
                            final textToCopy = _ripComponents!['formattedRipCode'].replaceAll(' ', '');
                            Clipboard.setData(ClipboardData(text: textToCopy));
                            if(mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Row(
                                    children: [
                                      const Icon(Icons.check_circle, color: Colors.white),
                                      const SizedBox(width: 8),
                                      Text(loc.verifyRipCopiedMessage),
                                    ],
                                  ),
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  backgroundColor: successColor,
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            }
                          },
                          borderRadius: BorderRadius.circular(24),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.copy_outlined,
                                  size: 18,
                                  color: Colors.black87,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  loc.verifyRipCopyButton,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8), // Espace entre les boutons

                    // Bouton Sauvegarder le compte
                    Expanded(
                      child: Material(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(24),
                        child: InkWell(
                          onTap: () => _navigateToSaveScreen(context),
                          borderRadius: BorderRadius.circular(24),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.save_outlined,
                                  size: 18,
                                  color: Colors.black87,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  Localizations.localeOf(context).languageCode == 'en'
                                      ? 'Save'
                                      : Localizations.localeOf(context).languageCode == 'ar'
                                          ? 'حفظ'
                                          : 'Sauver',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _calculateCCPKey(String ccpNumber) {
    try {
      int ac = 0;
      int j = 0;
      for (int ind = 4; ind < 4 + ccpNumber.length; ind++) {
        ac += (int.parse(ccpNumber[ccpNumber.length - 1 - j]) * ind);
        j++;
      }
      ac = ac % 100;
      return ac.toString().padLeft(2, '0');
    } catch (e) {
      return '??';
    }
  }

  /// Naviguer vers la page de sauvegarde du compte avec vérification si le compte existe déjà
  Future<void> _navigateToSaveScreen(BuildContext context) async {
    final loc = AppLocalizations.of(context);

    if (_ripComponents != null && _isValid == true) {
      final ccpNumber = _ripComponents!['ccpNumber'];
      final ripCode = _ripComponents!['formattedRipCode'].replaceAll(' ', '');
      final ccpKey = int.parse(_calculateCCPKey(ccpNumber));
      final ripKey = int.parse(_ripComponents!['ripKey']);

      // Vérifier si le compte existe déjà
      final provider = Provider.of<CCPAccountsProvider>(context, listen: false);
      await provider.loadAccounts();
      final accounts = provider.accounts;
      final cleanRipCode = ripCode.replaceAll(' ', '');
      final accountExists = accounts.any((account) => account.ripCode.replaceAll(' ', '') == cleanRipCode);

      if (!mounted) return;

      if (accountExists) {
        final errorColor = const Color(0xFFD32F2F);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    loc.ccpFormAccountExistsError,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: errorColor,
            duration: const Duration(seconds: 3),
          ),
        );
        return;
      }

      // Naviguer vers la page de sauvegarde du compte
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SaveAccountScreen(
            ccpNumber: ccpNumber,
            ccpKey: ccpKey,
            ripKey: ripKey,
            ripCode: ripCode,
          ),
        ),
      );

      // Si un résultat est retourné (ID du compte), naviguer vers la page des comptes sauvegardés
      if (result != null && mounted) {
        // Naviguer vers la page des comptes sauvegardés et se positionner sur le compte nouvellement créé
        Navigator.pushReplacementNamed(
          context,
          '/saved_accounts',
          arguments: {'scrollToAccountId': result},
        );
      }
    }
  }
}
