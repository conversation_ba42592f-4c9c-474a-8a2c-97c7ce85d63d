import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider with ChangeNotifier {
  Locale? _locale;
  static const String _selectedLanguageCodeKey = 'selectedLanguageCode';
  static const String _needsRestartKey = 'needs_restart_after_locale_change';

  Locale? get locale => _locale;

  // Flag to indicate if app needs restart after locale change
  bool _needsRestart = false;
  bool get needsRestart => _needsRestart;

  LocaleProvider() {
    _initializeProvider();
  }

  Future<void> _initializeProvider() async {
    await _loadLocale();
    await _checkNeedsRestart();
  }

  Future<void> _loadLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? languageCode = prefs.getString(_selectedLanguageCodeKey);
      if (languageCode != null) {
        _locale = Locale(languageCode);
        // Utiliser WidgetsBinding pour éviter les conflits de dépendances
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
    } catch (e) {
      debugPrint('Error loading locale: $e');
    }
  }

  Future<void> _checkNeedsRestart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _needsRestart = prefs.getBool(_needsRestartKey) ?? false;
      if (_needsRestart) {
        // Réinitialiser le flag
        await prefs.setBool(_needsRestartKey, false);
        // Utiliser WidgetsBinding pour éviter les conflits de dépendances
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
    } catch (e) {
      debugPrint('Error checking restart flag: $e');
    }
  }

  Future<void> setLocale(Locale locale) async {
    if (_locale?.languageCode != locale.languageCode) {
      _locale = locale;

      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_selectedLanguageCodeKey, locale.languageCode);

        // Définir le flag pour indiquer que l'app doit être redémarrée
        await prefs.setBool(_needsRestartKey, true);
        _needsRestart = true;

        // Utiliser WidgetsBinding pour éviter les conflits de dépendances
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      } catch (e) {
        debugPrint('Error setting locale: $e');
      }
    }
  }

  // Méthode pour redémarrer l'application
  Future<void> restartApp() async {
    // Utiliser SystemNavigator pour redémarrer l'application
    await SystemNavigator.pop();
    // Note: Sur certaines plateformes, cela fermera simplement l'application
    // et l'utilisateur devra la relancer manuellement
  }

  void clearLocale() async {
    _locale = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_selectedLanguageCodeKey);
    notifyListeners();
  }
}
