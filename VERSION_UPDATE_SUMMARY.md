# Mise à jour de version - CCP RIP

## Nouvelle version
- **Version Name:** 2.2.0 (précédemment 2.1.2)
- **Version Code:** 6 (précédemment 5)
- **Format complet:** 2.2.0+6

## Fichiers modifiés

### Configuration principale
- `pubspec.yaml` - Version mise à jour de `2.1.2+5` vers `2.2.0+6`

### Versions par défaut dans le code (fallback)
- `lib/screens/main_screen.dart` - Version par défaut: `2.1.0` → `2.2.0`
- `lib/screens/app_info_screen.dart` - Version par défaut: `2.1.0` → `2.2.0`
- `lib/widgets/app_drawer.dart` - Version par défaut: `2.1.2` → `2.2.0`
- `lib/screens/privacy_policy_screen.dart` - Version par défaut: `2.1.0` → `2.2.0`

### Documentation
- `SUPPRESSION_GOOGLE_DRIVE.md` - Ajout de la nouvelle version dans le titre

## Changements automatiques
Les fichiers suivants utilisent automatiquement la version de `pubspec.yaml` :
- `android/app/build.gradle.kts` - Utilise `flutter.versionCode` et `flutter.versionName`
- `ios/Runner/Info.plist` - Utilise `$(FLUTTER_BUILD_NAME)` et `$(FLUTTER_BUILD_NUMBER)`

## Vérification
- ✅ APK de release généré avec succès
- ✅ Toutes les versions par défaut mises à jour
- ✅ Configuration Android et iOS automatiquement synchronisée

## Raison de la mise à jour
Cette mise à jour de version accompagne la suppression complète des fonctionnalités Google Drive pour résoudre le problème de rejet de Google Play Console.

## Prochaines étapes
1. Tester l'application avec la nouvelle version
2. Soumettre à Google Play Store
3. L'application devrait être approuvée sans demande d'identifiants de démonstration
